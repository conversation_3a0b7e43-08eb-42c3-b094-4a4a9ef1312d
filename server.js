require('dotenv').config();
const express = require('express');
const path = require('path');
const mongoose = require('mongoose');

const app = express();
const PORT = process.env.PORT || 3000;

// --- Database Connection ---
mongoose.connect(process.env.DATABASE_URL, { useNewUrlParser: true, useUnifiedTopology: true })
    .then(() => console.log('MongoDB connected successfully.'))
    .catch(err => console.error('MongoDB connection error:', err));

// --- Data Schema and Model ---
const dataSchema = new mongoose.Schema({
    content: {
        type: String,
        required: true
    },
    timestamp: {
        type: Date,
        default: Date.now
    }
});
const Data = mongoose.model('Data', dataSchema);

// --- Middleware ---
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

// --- API Endpoints ---
// Endpoint to save new data
app.post('/api/save-data', async (req, res) => {
    const { data } = req.body;
    if (!data) {
        return res.status(400).json({ message: 'No data provided.' });
    }
    
    const newData = new Data({ content: data });
    
    try {
        const savedData = await newData.save();
        res.status(201).json({ message: 'Data saved successfully!', savedData });
    } catch (error) {
        res.status(500).json({ message: 'Error saving data to the database.', error });
    }
});

// Endpoint to get all data
app.get('/api/get-data', async (req, res) => {
    try {
        const allData = await Data.find().sort({ timestamp: -1 }); // Get newest first
        res.status(200).json(allData);
    } catch (error) {
        res.status(500).json({ message: 'Error retrieving data from the database.', error });
    }
});

// --- Start Server ---
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});