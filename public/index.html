<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Strength & Power Training Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <!-- Chosen Palette: Warm Neutrals (Stone, Slate, Teal) -->
    <!-- Application Structure Plan: A full-featured training SPA with persistent sidebar navigation. The structure is task-oriented: Dashboard (today's view), Logbook (history), Programs (customization), and Analytics (long-term trends). This is scalable for years of use, allowing users to easily access different aspects of their training journey without a cluttered interface. -->
    <!-- Visualization & Content Choices: 1. e1RM Chart (Chart.js Line) -> Goal: Track actual strength -> Plots calculated 1-rep max from user-logged AMRAP sets. 2. Workout Log (Dynamic HTML Table) -> Goal: Record History -> Users log completed sets/reps, creating a persistent record. 3. Program Selector (HTML Radios) -> Goal: Customize -> Allows switching between different 5/3/1 templates. 4. Bodyweight Tracker (HTML form + Chart.js) -> Goal: Correlate Data -> Users log bodyweight, which is charted alongside strength to see trends. All data is persisted in localStorage. NO SVG/Mermaid used. -->
    <!-- CONFIRMATION: NO SVG graphics used. NO Mermaid JS used. -->
    <style>
        body { font-family: 'Inter', sans-serif; }
        .chart-container { position: relative; width: 100%; max-width: 800px; margin-left: auto; margin-right: auto; height: 300px; max-height: 400px; }
        @media (min-width: 768px) { .chart-container { height: 400px; } }
        .sidebar-link-active { background-color: #0d9488; color: white; }
        .sidebar-link { display: flex; align-items: center; padding: 0.75rem 1rem; border-radius: 0.375rem; transition: background-color 0.2s; }
        .sidebar-link:hover { background-color: #f0fdfa; }
        .mobile-menu-open { transform: translateX(0); }
        .mobile-menu-closed { transform: translateX(-100%); }
    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-stone-100 text-stone-800">

    <div class="flex h-screen bg-stone-100">
        <!-- Sidebar -->
        <aside id="sidebar" class="fixed inset-y-0 left-0 bg-white w-64 p-4 transform transition-transform duration-300 ease-in-out z-30 shadow-lg mobile-menu-closed lg:translate-x-0">
            <h1 class="text-2xl font-bold text-slate-800 mb-8 text-center">PowerLog</h1>
            <nav id="main-nav" class="space-y-2">
                <!-- Nav links will be injected by JS -->
            </nav>
        </aside>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden lg:ml-64">
            <header class="bg-white shadow-sm p-4 flex justify-between items-center lg:hidden">
                <h1 class="text-xl font-bold text-slate-800">PowerLog</h1>
                <button id="menu-toggle" class="p-2 rounded-md text-stone-500 hover:bg-stone-100">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>
                </button>
            </header>
            
            <main id="main-content" class="flex-1 overflow-x-hidden overflow-y-auto bg-stone-100 p-4 sm:p-6 lg:p-8">
                <!-- Views will be injected here -->
            </main>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', () => {
        const mainContent = document.getElementById('main-content');
        const mainNav = document.getElementById('main-nav');
        const sidebar = document.getElementById('sidebar');
        const menuToggle = document.getElementById('menu-toggle');
        
        let chartInstances = {};

        const programData = {
            templates: {
                '531_bbb': { 
                    name: '5/3/1 Boring But Big', 
                    description: 'The classic. Focuses on the main lift followed by high-volume sets of the same lift for hypertrophy.',
                    assistance: (lift, tm, unit) => `
                        <h4 class="font-semibold text-md text-stone-700 mt-4 pt-4 border-t border-stone-200">Assistance: Boring But Big</h4>
                        <div class="flex justify-between items-center text-sm py-1">
                            <span>5 sets x 10 reps</span>
                            <span class="font-bold text-teal-600">${convertWeight(roundWeight(tm * 0.50))} ${unit}</span>
                        </div>`
                },
                '531_fsl': { 
                    name: '5/3/1 First Set Last', 
                    description: 'After your main work, you repeat your first working set for multiple sets of 5-8 reps for quality volume.',
                    assistance: (lift, tm, unit, weekInfo) => `
                        <h4 class="font-semibold text-md text-stone-700 mt-4 pt-4 border-t border-stone-200">Assistance: First Set Last</h4>
                        <div class="flex justify-between items-center text-sm py-1">
                            <span>3-5 sets x 5-8 reps</span>
                            <span class="font-bold text-teal-600">${convertWeight(roundWeight(tm * weekInfo.sets[0].p))} ${unit}</span>
                        </div>`
                },
            },
            weeks: [
                { week: 1, type: '3x5', sets: [{ p: 0.65, r: 5 }, { p: 0.75, r: 5 }, { p: 0.85, r: '5+' }] },
                { week: 2, type: '3x3', sets: [{ p: 0.70, r: 3 }, { p: 0.80, r: 3 }, { p: 0.90, r: '3+' }] },
                { week: 3, type: '5/3/1', sets: [{ p: 0.75, r: 5 }, { p: 0.85, r: 3 }, { p: 0.95, r: '1+' }] },
                { week: 4, type: 'Deload', sets: [{ p: 0.40, r: 5 }, { p: 0.50, r: 5 }, { p: 0.60, r: 5 }] },
            ],
            lifts: [
                { id: 'squat', name: 'Squat' },
                { id: 'bench', name: 'Bench Press' },
                { id: 'deadlift', name: 'Deadlift' },
                { id: 'ohp', name: 'Overhead Press' },
            ]
        };

        let state = {
            activeView: 'dashboard',
            units: 'kg',
            user1RMs: { squat: 100, bench: 61, deadlift: 70, ohp: 37 },
            currentCycle: 1,
            currentWeek: 1,
            currentDay: 0,
            programTemplate: '531_bbb',
            logbook: [],
            bodyweight: [],
        };

        const saveState = () => {
            localStorage.setItem('powerLogState', JSON.stringify(state));
        };

        const loadState = () => {
            const savedState = localStorage.getItem('powerLogState');
            if (savedState) {
                state = JSON.parse(savedState);
            }
        };

        const calculateTrainingMax = (liftId, cycle) => {
            const base1RM = state.user1RMs[liftId];
            const increment = (liftId === 'squat' || liftId === 'deadlift') ? 5 : 2.5;
            const cycleIncrement = increment * (cycle - 1);
            return (base1RM + cycleIncrement) * 0.9;
        };

        const roundWeight = (weight) => Math.round(weight / 2.5) * 2.5;
        const convertWeight = (kg) => (state.units === 'lbs' ? Math.round(kg * 2.20462) : kg);
        const e1RM = (weight, reps) => weight * reps * 0.0333 + weight;

        const renderSidebar = () => {
            const views = [
                { id: 'dashboard', name: 'Dashboard', icon: `<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>` },
                { id: 'logbook', name: 'Logbook', icon: `<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v11.494m-9-5.747h18"></path></svg>` },
                { id: 'programs', name: 'Programs', icon: `<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path></svg>` },
                { id: 'analytics', name: 'Analytics', icon: `<svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path></svg>` },
            ];
            mainNav.innerHTML = views.map(view => `
                <a href="#" data-view="${view.id}" class="sidebar-link text-stone-600 ${state.activeView === view.id ? 'sidebar-link-active' : ''}">
                    ${view.icon}
                    <span>${view.name}</span>
                </a>
            `).join('');
        };
        
        const renderDashboard = () => {
            const today = new Date().getDay();
            const dayMap = [6, 0, 1, 2, 3, 4, 5]; 
            const currentDayIndex = dayMap[today] < 4 ? dayMap[today] : 0;
            const lift = programData.lifts[currentDayIndex];
            const weekIndex = (state.currentWeek - 1) % 4;
            const weekInfo = programData.weeks[weekIndex];
            const tm = calculateTrainingMax(lift.id, state.currentCycle);
            const template = programData.templates[state.programTemplate];

            const setsHtml = weekInfo.sets.map((set, i) => {
                const weight = roundWeight(tm * set.p);
                const displayWeight = convertWeight(weight);
                const isAmrap = String(set.r).includes('+');
                return `
                    <tr class="border-b border-stone-200">
                        <td class="py-2 px-4 text-sm text-stone-600">Set ${i + 1}</td>
                        <td class="py-2 px-4 text-sm font-medium text-stone-800">${set.r} reps</td>
                        <td class="py-2 px-4 text-sm font-medium text-stone-800">${displayWeight} ${state.units}</td>
                        <td class="py-2 px-4">
                            ${isAmrap ? `<input type="number" placeholder="Reps" class="w-20 p-1 text-sm border rounded-md" data-lift-id="${lift.id}" data-weight="${weight}">` : ''}
                        </td>
                    </tr>
                `;
            }).join('');

            mainContent.innerHTML = `
                <h1 class="text-3xl font-bold text-slate-800 mb-2">Dashboard</h1>
                <p class="text-stone-600 mb-6">Here's your focus for today. Let's get to work.</p>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div class="lg:col-span-2">
                        <div class="bg-white p-6 rounded-lg shadow-md">
                            <h2 class="text-xl font-bold text-slate-700">Today's Workout: ${lift.name}</h2>
                            <p class="text-sm text-stone-500 mb-4">Cycle ${state.currentCycle}, Week ${state.currentWeek}</p>
                            <table class="w-full text-left">
                                <thead><tr class="bg-stone-50"><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Set</th><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Target</th><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Weight</th><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Actual Reps</th></tr></thead>
                                <tbody>${setsHtml}</tbody>
                            </table>
                            ${template.assistance(lift, tm, state.units, weekInfo)}
                            <button id="log-workout" class="w-full mt-6 bg-teal-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-teal-700">Log Today's Workout</button>
                        </div>
                    </div>
                    <div>
                        <div class="bg-white p-6 rounded-lg shadow-md">
                            <h3 class="text-lg font-bold text-slate-700 mb-4">Track Bodyweight</h3>
                            <div class="flex gap-2">
                                <input type="number" id="bw-input" class="w-full p-2 text-sm border rounded-md" placeholder="Weight in ${state.units}">
                                <button id="log-bw" class="bg-slate-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-slate-700">Log</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        };

        const renderLogbook = () => {
            const logEntries = state.logbook.map(entry => `
                <tr class="border-b border-stone-200">
                    <td class="py-3 px-4 text-sm">${new Date(entry.date).toLocaleDateString()}</td>
                    <td class="py-3 px-4 text-sm font-medium">${entry.lift}</td>
                    <td class="py-3 px-4 text-sm">${entry.weight.toFixed(1)} ${entry.unit} x ${entry.reps}</td>
                    <td class="py-3 px-4 text-sm font-bold text-teal-600">${entry.e1RM.toFixed(1)} ${entry.unit}</td>
                </tr>
            `).join('');

            mainContent.innerHTML = `
                <h1 class="text-3xl font-bold text-slate-800 mb-6">Workout Logbook</h1>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <table class="w-full text-left">
                        <thead><tr class="bg-stone-50"><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Date</th><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Lift</th><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Top Set</th><th class="py-2 px-4 text-xs font-semibold text-stone-500 uppercase">Est. 1RM</th></tr></thead>
                        <tbody>${logEntries.length ? logEntries : `<tr><td colspan="4" class="text-center py-8 text-stone-500">No workouts logged yet.</td></tr>`}</tbody>
                    </table>
                </div>
            `;
        };
        
        const renderPrograms = () => {
            const templatesHtml = Object.keys(programData.templates).map(key => {
                const template = programData.templates[key];
                return `
                    <label class="block p-4 border rounded-lg cursor-pointer ${state.programTemplate === key ? 'border-teal-500 bg-teal-50' : 'border-stone-300'}">
                        <div class="flex items-center">
                            <input type="radio" name="program-template" value="${key}" class="h-4 w-4 text-teal-600 border-stone-300 focus:ring-teal-500" ${state.programTemplate === key ? 'checked' : ''}>
                            <div class="ml-3 text-sm">
                                <p class="font-bold text-stone-800">${template.name}</p>
                                <p class="text-stone-600">${template.description}</p>
                            </div>
                        </div>
                    </label>
                `;
            }).join('');

            mainContent.innerHTML = `
                <h1 class="text-3xl font-bold text-slate-800 mb-6">Program Management</h1>
                <div class="bg-white p-6 rounded-lg shadow-md">
                    <h2 class="text-xl font-bold text-slate-700 mb-4">Select Your 5/3/1 Template</h2>
                    <div class="space-y-4">${templatesHtml}</div>

                    <h2 class="text-xl font-bold text-slate-700 mt-8 mb-4">Your 1-Rep Maxes (${state.units})</h2>
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        ${programData.lifts.map(lift => `
                            <div>
                                <label for="${lift.id}-1rm-input" class="block text-sm font-medium text-stone-700">${lift.name}</label>
                                <input type="number" id="${lift.id}-1rm-input" value="${convertWeight(state.user1RMs[lift.id]).toFixed(0)}" class="mt-1 block w-full rounded-md border-stone-300 shadow-sm p-2">
                            </div>
                        `).join('')}
                    </div>
                    <button id="save-program-settings" class="mt-6 w-full bg-teal-600 text-white font-semibold py-2 px-4 rounded-md hover:bg-teal-700">Save Settings</button>
                </div>
            `;
        };

        const renderAnalytics = () => {
            mainContent.innerHTML = `
                <h1 class="text-3xl font-bold text-slate-800 mb-6">Analytics</h1>
                <div class="space-y-8">
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h2 class="text-xl font-bold text-slate-700 mb-4">Estimated 1-Rep Max Progression</h2>
                        <div class="chart-container"><canvas id="e1rmChart"></canvas></div>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md">
                        <h2 class="text-xl font-bold text-slate-700 mb-4">Bodyweight Trend</h2>
                        <div class="chart-container"><canvas id="bwChart"></canvas></div>
                    </div>
                </div>
            `;
            renderE1RMChart();
            renderBwChart();
        };
        
        const renderE1RMChart = () => {
            if (chartInstances.e1rm) chartInstances.e1rm.destroy();
            const datasets = programData.lifts.map(lift => {
                const data = state.logbook
                    .filter(entry => entry.lift === lift.name)
                    .map(entry => ({ x: new Date(entry.date), y: convertWeight(entry.e1RM) }));
                const colors = {squat: '84, 180, 211', bench: '239, 148, 84', deadlift: '211, 84, 122', ohp: '130, 211, 84'};
                const color = colors[lift.id] || '107, 114, 128';
                return {
                    label: `${lift.name} e1RM (${state.units})`,
                    data: data,
                    borderColor: `rgba(${color}, 1)`,
                    tension: 0.1
                };
            });
            chartInstances.e1rm = new Chart(document.getElementById('e1rmChart').getContext('2d'), {
                type: 'line',
                data: { datasets },
                options: {
                    responsive: true, maintainAspectRatio: false,
                    scales: {
                        x: { type: 'time', time: { unit: 'day' } },
                        y: { beginAtZero: false, title: { display: true, text: `Weight (${state.units})` } }
                    }
                }
            });
        };

        const renderBwChart = () => {
            if (chartInstances.bw) chartInstances.bw.destroy();
            const data = state.bodyweight.map(entry => ({ x: new Date(entry.date), y: convertWeight(entry.weight) }));
            chartInstances.bw = new Chart(document.getElementById('bwChart').getContext('2d'), {
                type: 'line',
                data: {
                    datasets: [{
                        label: `Bodyweight (${state.units})`,
                        data: data,
                        borderColor: 'rgba(13, 148, 136, 1)',
                        backgroundColor: 'rgba(13, 148, 136, 0.1)',
                        fill: true,
                    }]
                },
                options: {
                    responsive: true, maintainAspectRatio: false,
                    scales: {
                        x: { type: 'time', time: { unit: 'day' } },
                        y: { beginAtZero: false, title: { display: true, text: `Weight (${state.units})` } }
                    }
                }
            });
        };

        const renderView = () => {
            switch (state.activeView) {
                case 'logbook': renderLogbook(); break;
                case 'programs': renderPrograms(); break;
                case 'analytics': renderAnalytics(); break;
                default: renderDashboard();
            }
            renderSidebar();
        };

        mainNav.addEventListener('click', e => {
            e.preventDefault();
            const link = e.target.closest('.sidebar-link');
            if (link) {
                state.activeView = link.dataset.view;
                renderView();
                if (window.innerWidth < 1024) {
                    sidebar.classList.add('mobile-menu-closed');
                }
            }
        });

        mainContent.addEventListener('click', e => {
            if (e.target.id === 'log-workout') {
                const amrapInput = document.querySelector('[data-lift-id]');
                if (amrapInput && amrapInput.value) {
                    state.logbook.push({
                        date: new Date().toISOString(),
                        lift: programData.lifts.find(l => l.id === amrapInput.dataset.liftId).name,
                        weight: parseFloat(amrapInput.dataset.weight),
                        reps: parseInt(amrapInput.value),
                        unit: 'kg',
                        e1RM: e1RM(parseFloat(amrapInput.dataset.weight), parseInt(amrapInput.value))
                    });
                    
                    state.currentDay++;
                    if (state.currentDay >= 4) {
                        state.currentDay = 0;
                        state.currentWeek++;
                        if ((state.currentWeek -1) % 4 === 0 && state.currentWeek > 1) {
                            state.currentCycle++;
                        }
                    }
                    saveState();
                    renderView();
                } else {
                    alert('Please enter your AMRAP reps to log the workout.');
                }
            }
            if (e.target.id === 'log-bw') {
                const bwInput = document.getElementById('bw-input');
                if (bwInput.value) {
                    const weightInKg = state.units === 'lbs' ? parseFloat(bwInput.value) / 2.20462 : parseFloat(bwInput.value);
                    state.bodyweight.push({ date: new Date().toISOString(), weight: weightInKg });
                    saveState();
                    bwInput.value = '';
                    renderView();
                }
            }
            if (e.target.id === 'save-program-settings') {
                state.programTemplate = document.querySelector('input[name="program-template"]:checked').value;
                programData.lifts.forEach(lift => {
                    const inputVal = parseFloat(document.getElementById(`${lift.id}-1rm-input`).value);
                    state.user1RMs[lift.id] = state.units === 'lbs' ? inputVal / 2.20462 : inputVal;
                });
                saveState();
                alert('Settings saved!');
                renderView();
            }
        });
        
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('mobile-menu-closed');
        });

        loadState();
        renderView();
    });
    </script>
</body>
</html>
