document.addEventListener('DOMContentLoaded', () => {
    const form = document.getElementById('data-form');
    const resultsContainer = document.getElementById('results-container');
    const userInput = document.getElementById('user-input');

    // Function to fetch and display all data
    const fetchData = async () => {
        try {
            const response = await fetch('/api/get-data');
            if (response.ok) {
                const dataItems = await response.json();
                if (dataItems.length > 0) {
                    resultsContainer.innerHTML = dataItems.map(item => 
                        `<p>${item.content} <em>(Saved on: ${new Date(item.timestamp).toLocaleString()})</em></p>`
                    ).join('');
                } else {
                    resultsContainer.innerHTML = '<p>No data has been saved yet.</p>';
                }
            } else {
                resultsContainer.innerHTML = '<p>Could not retrieve data.</p>';
            }
        } catch (error) {
            console.error('Fetch error:', error);
            resultsContainer.innerHTML = '<p>An error occurred while fetching data.</p>';
        }
    };

    // Handle form submission
    form.addEventListener('submit', async (event) => {
        event.preventDefault();
        
        const dataToSave = userInput.value;
        if (!dataToSave) return;

        try {
            const response = await fetch('/api/save-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ data: dataToSave }),
            });

            if (response.ok) {
                form.reset();
                fetchData(); // Refresh the displayed data
            } else {
                alert('Error saving data. Please try again.');
            }
        } catch (error) {
            console.error('Submit error:', error);
            alert('An error occurred. Please check the console.');
        }
    });

    // Initial data load when the page is ready
    fetchData();
});